<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Attendance;
use App\Models\Shift;
use App\Models\EmployeeProfile;
use Carbon\Carbon;

class ValidateAttendanceData extends Command
{
    protected $signature = 'attendance:validate {--days=7 : Number of days to check}';
    protected $description = 'Validate attendance data for inconsistencies and potential issues';

    public function handle()
    {
        $days = $this->option('days');
        $startDate = Carbon::today()->subDays($days - 1);
        $endDate = Carbon::today();

        $this->info("Validating attendance data from {$startDate->toDateString()} to {$endDate->toDateString()}");
        $this->newLine();

        $issues = [];

        // Check 1: Negative hours calculations
        $this->info('🔍 Checking for negative hours calculations...');
        $negativeHours = $this->checkNegativeHours($startDate, $endDate);
        if (!empty($negativeHours)) {
            $issues[] = 'Negative Hours';
            $this->error("❌ Found {count($negativeHours)} records with negative hours:");
            foreach ($negativeHours as $record) {
                $this->line("   - Employee {$record['employee_id']}: {$record['hours']}h on {$record['date']}");
            }
        } else {
            $this->info('✅ No negative hours found');
        }

        // Check 2: Shift configuration issues
        $this->info('🔍 Checking shift configurations...');
        $shiftIssues = $this->checkShiftConfigurations();
        if (!empty($shiftIssues)) {
            $issues[] = 'Shift Configuration';
            $this->error("❌ Found shift configuration issues:");
            foreach ($shiftIssues as $issue) {
                $this->line("   - {$issue}");
            }
        } else {
            $this->info('✅ Shift configurations look good');
        }

        // Check 3: Incomplete attendances
        $this->info('🔍 Checking incomplete attendances...');
        $incompleteCount = $this->checkIncompleteAttendances($startDate, $endDate);
        if ($incompleteCount > 0) {
            $this->warn("⚠️  Found {$incompleteCount} incomplete attendance records (missing check-out)");
        } else {
            $this->info('✅ No incomplete attendances found');
        }

        // Check 4: Data consistency
        $this->info('🔍 Checking data consistency...');
        $consistencyIssues = $this->checkDataConsistency($startDate, $endDate);
        if (!empty($consistencyIssues)) {
            $issues[] = 'Data Consistency';
            $this->error("❌ Found data consistency issues:");
            foreach ($consistencyIssues as $issue) {
                $this->line("   - {$issue}");
            }
        } else {
            $this->info('✅ Data consistency looks good');
        }

        $this->newLine();
        if (empty($issues)) {
            $this->info('🎉 All validation checks passed!');
        } else {
            $this->error('❌ Validation found issues in: ' . implode(', ', $issues));
            $this->info('💡 Check the output above for details and consider running fixes.');
        }

        return empty($issues) ? 0 : 1;
    }

    private function checkNegativeHours($startDate, $endDate)
    {
        $negativeHours = [];
        
        $attendances = Attendance::with(['employeeProfile', 'shift'])
            ->whereNotNull('check_out_time')
            ->whereBetween('check_in_time', [$startDate, $endDate])
            ->get();

        foreach ($attendances as $attendance) {
            $checkIn = Carbon::parse($attendance->check_in_time);
            $checkOut = Carbon::parse($attendance->check_out_time);
            
            // Handle overnight shifts
            if ($attendance->shift && $attendance->shift->spans_two_days && $checkOut->lt($checkIn)) {
                $checkOut->addDay();
            }
            
            $minutes = $checkIn->diffInMinutes($checkOut);
            $hours = round($minutes / 60, 2);
            
            if ($hours < 0) {
                $negativeHours[] = [
                    'employee_id' => $attendance->employeeProfile->id,
                    'date' => $checkIn->toDateString(),
                    'hours' => $hours,
                    'check_in' => $checkIn->format('Y-m-d H:i:s'),
                    'check_out' => $attendance->check_out_time,
                    'shift' => $attendance->shift->name ?? 'Unknown',
                ];
            }
        }

        return $negativeHours;
    }

    private function checkShiftConfigurations()
    {
        $issues = [];
        
        $shifts = Shift::all();
        
        foreach ($shifts as $shift) {
            $startTime = Carbon::parse($shift->start_time);
            $endTime = Carbon::parse($shift->end_time);
            
            // Check if overnight flag matches actual times
            $isActuallyOvernight = $endTime->lt($startTime);
            
            if ($isActuallyOvernight && !$shift->spans_two_days) {
                $issues[] = "Shift '{$shift->name}' appears to be overnight ({$shift->start_time} - {$shift->end_time}) but spans_two_days is false";
            }
            
            if (!$isActuallyOvernight && $shift->spans_two_days) {
                $issues[] = "Shift '{$shift->name}' is marked as overnight but times don't span midnight ({$shift->start_time} - {$shift->end_time})";
            }
        }
        
        return $issues;
    }

    private function checkIncompleteAttendances($startDate, $endDate)
    {
        return Attendance::whereNull('check_out_time')
            ->whereBetween('check_in_time', [$startDate, $endDate])
            ->count();
    }

    private function checkDataConsistency($startDate, $endDate)
    {
        $issues = [];
        
        // Check for multiple check-ins on same day
        $duplicateCheckIns = Attendance::selectRaw('employee_profile_id, DATE(check_in_time) as date, COUNT(*) as count')
            ->whereBetween('check_in_time', [$startDate, $endDate])
            ->groupBy('employee_profile_id', 'date')
            ->having('count', '>', 1)
            ->get();
            
        if ($duplicateCheckIns->count() > 0) {
            $issues[] = "Found {$duplicateCheckIns->count()} days with multiple check-ins for the same employee";
        }
        
        // Check for unrealistic hours (> 24 hours)
        $unrealisticHours = Attendance::with('shift')
            ->whereNotNull('check_out_time')
            ->whereBetween('check_in_time', [$startDate, $endDate])
            ->get()
            ->filter(function ($attendance) {
                $checkIn = Carbon::parse($attendance->check_in_time);
                $checkOut = Carbon::parse($attendance->check_out_time);
                
                if ($attendance->shift && $attendance->shift->spans_two_days && $checkOut->lt($checkIn)) {
                    $checkOut->addDay();
                }
                
                $hours = $checkIn->diffInHours($checkOut);
                return $hours > 24;
            });
            
        if ($unrealisticHours->count() > 0) {
            $issues[] = "Found {$unrealisticHours->count()} attendance records with > 24 hours worked";
        }
        
        return $issues;
    }
}
