<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use App\Models\Shift;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Update the Weekend Shift policy to have proper grace periods
        $weekendShift = Shift::where('name', 'Weekend Shift')->first();
        if ($weekendShift && $weekendShift->policy) {
            $weekendShift->policy->update([
                'early_leave_grace_period_minutes' => 10,
                'overtime_grace_period_minutes' => 15,
                'check_in_allowance_before_minutes' => 30,
            ]);
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Revert the Weekend Shift policy changes
        $weekendShift = Shift::where('name', 'Weekend Shift')->first();
        if ($weekendShift && $weekendShift->policy) {
            $weekendShift->policy->update([
                'early_leave_grace_period_minutes' => 0,
                'overtime_grace_period_minutes' => 15,
                'check_in_allowance_before_minutes' => 30,
            ]);
        }
    }
};
