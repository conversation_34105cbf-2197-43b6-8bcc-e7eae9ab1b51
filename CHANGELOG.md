# Attendance Tracker Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [Unreleased]

### Fixed
- **CRITICAL:** Fixed negative hours calculation in 7-day activity chart (-153h 36m issue)
  - Added proper overnight shift handling in `EmployeeDashboardController::getWeeklyActivityData()`
  - Added `max(0, ...)` protection to prevent negative time calculations
  - Fixed `diffInMinutes` parameter order for check-in/check-out times
- Fixed Day Shift incorrectly marked as overnight shift in database (`spans_two_days: 1 → 0`)
- Fixed checkbox binding issue in shift edit form (`v-model:checked` → `v-model` + `:checked`)

### Added
- Comprehensive change management system:
  - `CHANGELOG.md` for tracking all changes
  - `tests/Feature/AttendanceCalculationTest.php` for regression testing
  - `app/Console/Commands/ValidateAttendanceData.php` for data validation
  - `scripts/pre-commit-checks.sh` for automated validation
  - `DEVELOPMENT.md` with development guidelines
- Validation command: `php artisan attendance:validate`
- Pre-commit validation script to prevent regressions

### Changed
- Enhanced time calculation logic to prevent negative hours
- Improved overnight shift detection and handling
- Added safety checks for all time difference calculations

## [Previous Changes - To Be Documented]

### Known Issues to Track
- [ ] Attendance calculations for overnight shifts
- [ ] Grace period calculations in undertime logic
- [ ] Break time handling in worked hours
- [ ] Time zone handling consistency
- [ ] Chart visualization edge cases

## Testing Checklist

### Before Each Release
- [ ] Test Day Shift (9 AM - 5 PM) attendance calculations
- [ ] Test Night Shift (9 PM - 5 AM) attendance calculations  
- [ ] Test Weekend Shift attendance calculations
- [ ] Verify 7-day activity chart shows correct hours (no negatives)
- [ ] Verify monthly punctuality chart calculations
- [ ] Test incomplete attendance handling (missing check-out)
- [ ] Test undertime/overtime calculations with grace periods
- [ ] Test shift editing (checkbox states save correctly)
- [ ] Test attendance seeding doesn't create data inconsistencies

### Critical Scenarios
1. **Overnight Shift Edge Cases**
   - Check-in before midnight, check-out after midnight
   - Missing check-out on overnight shift
   - Grace period calculations across midnight

2. **Data Integrity**
   - Only one check-in and one check-out per day
   - No negative hours in any calculation
   - Consistent time zone handling

3. **UI/UX**
   - Chart tooltips show correct information
   - Color coding matches attendance status
   - Form submissions save correctly

## Development Guidelines

### Before Making Changes
1. Document the current behavior
2. Identify all affected components
3. Create test cases for edge scenarios
4. Update this changelog

### After Making Changes
1. Test all scenarios in the checklist
2. Update documentation
3. Commit with descriptive messages
4. Update changelog with specific changes

## Component Dependencies

### Attendance Calculations
- `EmployeeDashboardController::getWeeklyActivityData()`
- `AttendanceController::calculateOvertimeUndertime()`
- `WeeklyActivityChart.vue`
- `ActivityChart.vue`

### Shift Management
- `ShiftController` (store/update methods)
- `Shift` model (`spans_two_days` field)
- `Edit.vue` and `Create.vue` forms

### Database Seeders
- `DatabaseSeeder.php`
- `ShiftFactory.php`
- Attendance seeding logic
