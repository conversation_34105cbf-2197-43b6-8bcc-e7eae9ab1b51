<?php

namespace App\Http\Controllers;

use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use App\Models\Attendance;
use App\Models\EmployeeShift;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Illuminate\Validation\ValidationException;
use Carbon\Carbon;

class AttendanceController extends Controller
{
    use AuthorizesRequests; // This provides the authorize() method

    public function checkIn(Request $request)
    {
        $validated = $request->validate([
            'latitude' => 'required|numeric',
            'longitude' => 'required|numeric',
            'selfie' => 'required|string',
        ]);

        $user = Auth::user();
        $employeeProfile = $user->employeeProfile;
        $now = now();
        $today = $now->toDateString();

        $assignment = EmployeeShift::with(['shift.policy', 'workZone'])
            ->where('employee_profile_id', $employeeProfile->id)
            ->where('start_date', '<=', $today)
            ->where(fn($q) => $q->where('end_date', '>=', $today)->orWhereNull('end_date'))
            ->firstOrFail();

        $shift = $assignment->shift;
        $policy = $shift->policy;

        $shiftStartTime = Carbon::parse($today . ' ' . $shift->start_time);

        // If it's an overnight shift and we are in the early AM hours, the shift started yesterday.
        if ($shift->spans_two_days && $now->lt($shiftStartTime)) {
            $shiftStartTime->subDay();
        }

        $allowedCheckInStart = $shiftStartTime->copy()->subMinutes($policy->check_in_allowance_before_minutes);

        if ($now->isBefore($allowedCheckInStart)) {
            throw ValidationException::withMessages([
                'time' => "Check-in is too early. You can check in from " . $allowedCheckInStart->format('g:i A') . ".",
            ]);
        }
        
        $distance = $this->calculateDistance(
            $validated['latitude'],
            $validated['longitude'],
            $assignment->workZone->latitude,
            $assignment->workZone->longitude
        );

        if ($distance > $assignment->workZone->radius_meters) {
            throw ValidationException::withMessages(['location' => 'You are not within the allowed work zone radius.']);
        }
        
        $selfieImage = base64_decode(preg_replace('#^data:image/\w+;base64,#i', '', $validated['selfie']));
        $selfiePath = 'selfies/' . $user->id . '_checkin_' . time() . '.jpg';
        Storage::disk('public')->put($selfiePath, $selfieImage);

        $isLate = $now->gt($shiftStartTime->copy()->addMinutes($policy->late_grace_period_minutes));
        
        Attendance::create([
            'employee_profile_id' => $employeeProfile->id,
            'shift_id' => $shift->id,
            'check_in_time' => $now,
            'check_in_latitude' => $validated['latitude'],
            'check_in_longitude' => $validated['longitude'],
            'check_in_selfie_path' => $selfiePath,
            'status' => $isLate ? 'late' : 'on_time',
        ]);

        return redirect()->route('dashboard')->with('success', 'Checked in successfully!');
    }
    
    public function checkOut(Request $request, Attendance $attendance)
    {
        $this->authorize('update', $attendance);

        $validated = $request->validate([
            'latitude' => 'required|numeric',
            'longitude' => 'required|numeric',
            'selfie' => 'required|string',
        ]);

        $now = now();
        $shift = $attendance->shift;
        $policy = $shift->policy;

        // ### RESTORED CHECK-OUT TIME VALIDATION ###
        $checkInTime = Carbon::parse($attendance->check_in_time);
        $shiftEndTime = Carbon::parse($checkInTime->toDateString() . ' ' . $shift->end_time);

        if ($shift->spans_two_days && $shiftEndTime->lt($checkInTime)) {
            $shiftEndTime->addDay();
        }

        $earlyCheckoutLimit = $shiftEndTime->copy()->subMinutes($policy->early_leave_grace_period_minutes);
        
        if ($now->isBefore($earlyCheckoutLimit)) {
             throw ValidationException::withMessages([
                'time' => "Check-out is too early. You can check out after " . $earlyCheckoutLimit->format('g:i A') . ".",
            ]);
        }
        
        $selfieImage = base64_decode(preg_replace('#^data:image/\w+;base64,#i', '', $validated['selfie']));
        $selfiePath = 'selfies/' . Auth::id() . '_checkout_' . time() . '.jpg';
        Storage::disk('public')->put($selfiePath, $selfieImage);

        $shiftStartTime = Carbon::parse($checkInTime->toDateString() . ' ' . $shift->start_time);
        if ($shift->spans_two_days && $checkInTime->lt($shiftStartTime)) {
            $shiftStartTime->subDay();
        }

        $totalMinutesWorked = $checkInTime->diffInMinutes($now);
        $scheduledMinutes = $shiftStartTime->diffInMinutes($shiftEndTime);

        // Enhanced undertime/overtime calculation using shift policies
        [$overtime, $undertime] = $this->calculateOvertimeUndertime(
            $totalMinutesWorked,
            $scheduledMinutes,
            $shift,
            $policy
        );
        
        $attendance->update([
            'check_out_time' => $now,
            'check_out_latitude' => $validated['latitude'],
            'check_out_longitude' => $validated['longitude'],
            'check_out_selfie_path' => $selfiePath,
            'overtime_minutes' => $overtime,
            'undertime_minutes' => $undertime,
        ]);

        return redirect()->route('dashboard')->with('success', 'Checked out successfully!');
    }

    private function calculateDistance($lat1, $lon1, $lat2, $lon2) {
        $earthRadius = 6371000;
        $latFrom = deg2rad($lat1);
        $lonFrom = deg2rad($lon1);
        $latTo = deg2rad($lat2);
        $lonTo = deg2rad($lon2);
        $lonDelta = $lonTo - $lonFrom;
        $a = pow(cos($latTo) * sin($lonDelta), 2) + pow(cos($latFrom) * sin($latTo) - sin($latFrom) * cos($latTo) * cos($lonDelta), 2);
        $b = sin($latFrom) * sin($latTo) + cos($latFrom) * cos($latTo) * cos($lonDelta);
        $angle = atan2(sqrt($a), $b);
        return $angle * $earthRadius;
    }

    public function preCheck(Request $request)
    {
        $validated = $request->validate([
            'latitude' => 'required|numeric',
            'longitude' => 'required|numeric',
        ]);

        $user = Auth::user();
        $employeeProfile = $user->employeeProfile;
        $now = now();

        $assignment = EmployeeShift::with(['shift.policy', 'workZone'])
            ->where('employee_profile_id', $employeeProfile->id)
            ->whereDate('start_date', '<=', $now->toDateString())
            ->where(fn($q) => $q->whereDate('end_date', '>=', $now->toDateString())->orWhereNull('end_date'))
            ->first();

        if (!$assignment) {
            return response()->json(['message' => 'You do not have a shift assigned for today.'], 422);
        }

        // --- NEW, SIMPLIFIED, AND CORRECT TIME LOGIC ---

        $shift = $assignment->shift;
        $policy = $shift->policy;

        // 1. Define today's shift start and end times
        $shiftStartTime = Carbon::parse($now->toDateString() . ' ' . $shift->start_time);
        $shiftEndTime = Carbon::parse($now->toDateString() . ' ' . $shift->end_time);

        // 2. Define the absolute check-in window start and end
        $windowStart = $shiftStartTime->copy()->subMinutes($policy->check_in_allowance_before_minutes);
        $windowEnd = $shiftEndTime;

        // 3. For overnight shifts, the end of the window is simply on the next day.
        if ($shift->spans_two_days && $windowEnd->lt($windowStart)) {
            $windowEnd->addDay();
        }

        // --- End of new logic ---

        // 4. Perform the final, simple validation
        if (!$now->between($windowStart, $windowEnd, true)) { // `true` makes the check inclusive
            $errorMessage = $now->isBefore($windowStart)
                ? "Check-in is too early. You can check in from " . $windowStart->format('g:i A') . "."
                : "Check-in window has closed. Your shift ended at " . $windowEnd->format('g:i A') . ".";
            return response()->json(['message' => $errorMessage], 422);
        }
        
        // Geo-fence validation remains the same
        $distance = $this->calculateDistance($validated['latitude'], $validated['longitude'], $assignment->workZone->latitude, $assignment->workZone->longitude);
        if ($distance > $assignment->workZone->radius_meters) {
            return response()->json(['message' => 'You are not within the allowed work zone radius.'], 422);
        }
        
        return response()->json(['message' => 'Validation successful.'], 200);
    }

    public function validateCheckoutTime(Request $request, Attendance $attendance)
    {
        // Ensure the person trying to validate owns this attendance record
        $this->authorize('update', $attendance);

        $now = now();
        $shift = $attendance->shift;
        $policy = $shift->policy;

        // Use the same robust logic from your main checkOut method
        $checkInTime = Carbon::parse($attendance->check_in_time);
        $shiftEndTime = Carbon::parse($checkInTime->toDateString() . ' ' . $shift->end_time);

        if ($shift->spans_two_days && $shiftEndTime->lt($checkInTime)) {
            $shiftEndTime->addDay();
        }

        $earlyCheckoutLimit = $shiftEndTime->copy()->subMinutes($policy->early_leave_grace_period_minutes);
        
        // The core validation check
        if ($now->isBefore($earlyCheckoutLimit)) {
            return response()->json([
                'message' => "Check-out is too early. You can check out after " . $earlyCheckoutLimit->format('g:i A') . "."
            ], 422); // Return a specific JSON error
        }

        // If the time is valid, return success
        return response()->json(['message' => 'Time validation successful.'], 200);
    }

    /**
     * Enhanced overtime/undertime calculation using shift policies.
     *
     * This method implements the improved logic where:
     * - Minimum required work time = scheduled_hours - late_grace_period - early_grace_period
     * - If actual work time < minimum required time → Undertime
     * - If actual work time > scheduled_hours + overtime_grace_period → Overtime
     *
     * @param int $totalMinutesWorked Total minutes from check-in to check-out
     * @param int $scheduledMinutes Total scheduled minutes for the shift
     * @param \App\Models\Shift $shift The shift model
     * @param \App\Models\ShiftPolicy $policy The shift policy model
     * @return array [overtime_minutes, undertime_minutes]
     */
    private function calculateOvertimeUndertime($totalMinutesWorked, $scheduledMinutes, $shift, $policy)
    {
        // We track attendance time (check-in to check-out), not payable time
        // So we use total minutes worked, not payable minutes

        // Calculate minimum required work time using grace periods
        // This accounts for both late arrival and early departure allowances
        $minimumRequiredMinutes = $scheduledMinutes - $policy->late_grace_period_minutes - $policy->early_leave_grace_period_minutes;

        // Ensure minimum doesn't go below zero
        $minimumRequiredMinutes = max(0, $minimumRequiredMinutes);

        $overtime = 0;
        $undertime = 0;

        // Check for undertime first
        if ($totalMinutesWorked < $minimumRequiredMinutes) {
            $undertime = $minimumRequiredMinutes - $totalMinutesWorked;
        }
        // Check for overtime (only if not undertime)
        elseif ($totalMinutesWorked > $scheduledMinutes + $policy->overtime_grace_period_minutes) {
            $overtime = $totalMinutesWorked - $scheduledMinutes - $policy->overtime_grace_period_minutes;
        }

        return [$overtime, $undertime];
    }
}