# Development Guidelines

## Change Management Process

To prevent regressions and maintain code quality, follow this process for all changes:

### Before Making Changes

1. **Document Current Behavior**
   ```bash
   # Test current functionality
   php artisan attendance:validate
   php artisan test tests/Feature/AttendanceCalculationTest.php
   ```

2. **Identify Impact**
   - List all components that might be affected
   - Check dependencies in `CHANGELOG.md`
   - Consider edge cases (overnight shifts, incomplete attendance, etc.)

3. **Create Test Cases**
   - Add tests for new functionality
   - Add tests for edge cases
   - Update existing tests if behavior changes

### Making Changes

1. **Small, Focused Changes**
   - Make one logical change at a time
   - Avoid mixing feature changes with bug fixes
   - Keep commits atomic and well-described

2. **Test Continuously**
   ```bash
   # Run validation after each change
   ./scripts/pre-commit-checks.sh
   ```

### After Making Changes

1. **Validate Everything Works**
   ```bash
   # Full validation suite
   php artisan attendance:validate --days=30
   php artisan test tests/Feature/AttendanceCalculationTest.php
   ```

2. **Update Documentation**
   - Update `CHANGELOG.md` with specific changes
   - Update this file if process changes
   - Add comments for complex logic

3. **Manual Testing Checklist**
   - [ ] Day shift attendance calculations
   - [ ] Night shift attendance calculations
   - [ ] 7-day activity chart (no negative values)
   - [ ] Monthly punctuality chart
   - [ ] Shift editing form (checkbox behavior)
   - [ ] Incomplete attendance handling

## Quick Commands

```bash
# Validate attendance data
php artisan attendance:validate

# Run attendance tests
php artisan test tests/Feature/AttendanceCalculationTest.php

# Run all pre-commit checks
./scripts/pre-commit-checks.sh

# Check for negative hours in last 7 days
php artisan attendance:validate --days=7

# Recalculate attendance undertime (if needed)
php artisan attendance:recalculate-undertime
```

## Common Issues & Solutions

### Negative Hours in Charts
**Symptoms:** Chart shows negative values like "-153h 36m"
**Causes:** 
- Incorrect overnight shift handling
- Missing `max(0, ...)` in time calculations
- Wrong `diffInMinutes` parameter order

**Solution:**
```php
// Always ensure positive hours
$minutes = max(0, $checkIn->diffInMinutes($checkOut));

// Handle overnight shifts
if ($shift->spans_two_days && $checkOut->lt($checkIn)) {
    $checkOut->addDay();
}
```

### Checkbox Not Saving
**Symptoms:** Checkbox appears unchecked but database shows true
**Causes:** Wrong v-model binding for UI components

**Solution:**
```vue
<!-- Correct for UI Checkbox -->
<Checkbox v-model="form.field" :checked="form.field" />

<!-- Wrong -->
<Checkbox v-model:checked="form.field" />
```

### Shift Configuration Issues
**Symptoms:** Day shift treated as overnight
**Causes:** Database `spans_two_days` doesn't match actual times

**Solution:**
```bash
# Check and fix in tinker
php artisan tinker
$shift = App\Models\Shift::where('name', 'Day Shift')->first();
$shift->spans_two_days = false;
$shift->save();
```

## File Structure

```
├── CHANGELOG.md                          # Track all changes
├── DEVELOPMENT.md                        # This file
├── tests/Feature/AttendanceCalculationTest.php  # Core tests
├── app/Console/Commands/ValidateAttendanceData.php  # Validation
├── scripts/pre-commit-checks.sh          # Pre-commit validation
└── app/Http/Controllers/
    ├── EmployeeDashboardController.php   # Weekly activity calculations
    ├── AttendanceController.php          # Check-in/out logic
    └── Admin/ShiftController.php         # Shift management
```

## Emergency Fixes

If you discover a critical issue in production:

1. **Immediate Fix**
   ```bash
   # Fix the data issue directly
   php artisan tinker
   # ... make necessary changes
   ```

2. **Validate Fix**
   ```bash
   php artisan attendance:validate
   ```

3. **Document**
   - Add to `CHANGELOG.md` under "Emergency Fixes"
   - Create proper test case
   - Plan permanent solution

## Best Practices

- **Always test edge cases:** overnight shifts, incomplete attendance, missing data
- **Use validation commands:** Don't rely only on manual testing
- **Keep changes small:** Easier to debug and revert
- **Document everything:** Future you will thank present you
- **Test before committing:** Use the pre-commit script religiously
