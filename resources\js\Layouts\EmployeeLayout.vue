<script setup>
import { ref, watch, computed } from 'vue';
import { Link, usePage } from '@inertiajs/vue3';
import { Home, PanelLeft } from 'lucide-vue-next'; // Simplified icons for employee
import { But<PERSON> } from '@/Components/ui/button';
import { Sheet, SheetContent, SheetTrigger } from '@/Components/ui/sheet';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuLabel, DropdownMenuSeparator, DropdownMenuTrigger } from '@/Components/ui/dropdown-menu';
import { Avatar, AvatarFallback } from '@/Components/ui/avatar';
import ApplicationLogo from '@/Components/ApplicationLogo.vue';

// This entire script block is a simplified version of your working AdminLayout script
const showNotification = ref(false);
const notificationMessage = ref('');
const notificationIsError = ref(false);

const isMobileMenuOpen = ref(false);
const page = usePage();
const user = page.props.auth.user;
const employeeProfile = computed(() => page.props.employeeProfile);

// Use employee profile name if available, fallback to user name
const displayName = computed(() => {
    if (employeeProfile.value?.full_name) {
        return employeeProfile.value.full_name;
    }
    return user?.name || '';
});

const initials = computed(() => {
    if (employeeProfile.value?.first_name && employeeProfile.value?.last_name) {
        return (employeeProfile.value.first_name.charAt(0) + employeeProfile.value.last_name.charAt(0)).toUpperCase();
    }
    return user?.name ? user.name.substring(0, 2).toUpperCase() : '';
});

watch(() => page.props.flash, (flash) => {
    if (flash?.success) {
        notificationMessage.value = flash.success;
        notificationIsError.value = false;
        showNotification.value = true;
        setTimeout(() => { showNotification.value = false; }, 4000);
    }
    if (flash?.error) {
        notificationMessage.value = flash.error;
        notificationIsError.value = true;
        showNotification.value = true;
        setTimeout(() => { showNotification.value = false; }, 4000);
    }
}, { deep: true });

const navItems = [
    { title: 'Dashboard', href: route('dashboard'), icon: Home, startsWith: '/dashboard' },
    // We will add more links like "My Attendance" here later
];
</script>

<template>
    <div>
        <!-- Notification Div (from your working AdminLayout) -->
        <div v-if="showNotification" 
             :class="[
                'fixed top-5 left-1/2 -translate-x-1/2 z-[9999] text-white p-4 rounded-md shadow-lg',
                notificationIsError ? 'bg-red-600' : 'bg-green-600'
             ]">
            {{ notificationMessage }}
        </div>

        <!-- ### THE MAIN LAYOUT STRUCTURE - A DIRECT COPY OF ADMINLAYOUT ### -->
        <div class="min-h-screen w-full">
            <aside class="hidden md:flex flex-col fixed inset-y-0 left-0 z-10 w-56 border-r bg-background">
                <div class="flex h-14 items-center border-b px-6">
                    <Link href="/" class="flex items-center gap-2 font-semibold">
                        <ApplicationLogo class="block h-9 w-auto" />
                        <span>Time Tracker</span>
                    </Link>
                </div>
                <nav class="flex-1 overflow-auto py-4 px-4 text-sm font-medium">
                    <Link v-for="item in navItems" :key="item.title" :href="item.href" class="flex items-center gap-3 rounded-lg px-3 py-2 text-muted-foreground transition-all hover:text-primary" :class="{ 'bg-muted text-primary': page.url.startsWith(item.startsWith) }">
                        <component :is="item.icon" class="h-4 w-4" />
                        {{ item.title }}
                    </Link>
                </nav>
            </aside>

            <div class="flex flex-col md:ml-56">
                <header v-if="$slots.header || $slots.actions" class="flex h-14 items-center gap-4 border-b bg-background px-4 lg:px-6 sticky top-0 z-30">
                    <Sheet v-model:open="isMobileMenuOpen">
                        <SheetTrigger as-child>
                            <Button variant="outline" size="icon" class="shrink-0 md:hidden"><PanelLeft class="h-5 w-5" /></Button>
                        </SheetTrigger>
                        <SheetContent side="left" class="flex flex-col bg-background p-6">
                           <nav class="grid gap-2 text-base font-medium">
                                <Link v-for="item in navItems" :key="`mobile-${item.title}`" :href="item.href" @click="isMobileMenuOpen = false" class="flex items-center gap-3 rounded-lg px-3 py-2 text-muted-foreground transition-all hover:text-primary">
                                    <component :is="item.icon" class="h-5 w-5" />
                                    {{ item.title }}
                                </Link>
                           </nav>
                        </SheetContent>
                    </Sheet>
                    
                    <div class="flex-1">
                        <slot name="header" />
                    </div>

                    <div class="flex items-center gap-4">
                        <slot name="actions" />
                        <DropdownMenu>
                            <DropdownMenuTrigger as-child>
                                <Button variant="secondary" size="icon" class="rounded-full">
                                    <Avatar class="h-8 w-8"><AvatarFallback>{{ initials }}</AvatarFallback></Avatar>
                                </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                                <DropdownMenuLabel>My Account</DropdownMenuLabel>
                                <DropdownMenuSeparator />
                                <DropdownMenuItem @click="$inertia.visit(route('profile.edit'))">Profile</DropdownMenuItem>
                                <DropdownMenuSeparator />
                                <DropdownMenuItem>
                                    <Link :href="route('logout')" method="post" as="button" class="w-full text-left">Log Out</Link>
                                </DropdownMenuItem>
                            </DropdownMenuContent>
                        </DropdownMenu>
                    </div>
                </header>
                <main class="flex-1">
                    <slot />
                </main>
            </div>
        </div>
    </div>
</template>