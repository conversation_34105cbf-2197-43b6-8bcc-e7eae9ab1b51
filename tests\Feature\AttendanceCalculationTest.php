<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\EmployeeProfile;
use App\Models\Shift;
use App\Models\ShiftPolicy;
use App\Models\Attendance;
use App\Models\EmployeeShift;
use App\Models\WorkZone;
use Carbon\Carbon;
use Illuminate\Foundation\Testing\RefreshDatabase;

class AttendanceCalculationTest extends TestCase
{
    use RefreshDatabase;

    protected $user;
    protected $employeeProfile;
    protected $dayShift;
    protected $nightShift;
    protected $workZone;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create test user and employee profile
        $this->user = User::factory()->create();
        $this->employeeProfile = EmployeeProfile::factory()->create([
            'user_id' => $this->user->id,
        ]);

        // Create shifts
        $this->dayShift = Shift::factory()->create([
            'name' => 'Day Shift',
            'start_time' => '09:00:00',
            'end_time' => '17:00:00',
            'spans_two_days' => false,
            'unpaid_break_minutes' => 60,
        ]);

        $this->nightShift = Shift::factory()->create([
            'name' => 'Night Shift', 
            'start_time' => '21:00:00',
            'end_time' => '05:00:00',
            'spans_two_days' => true,
            'unpaid_break_minutes' => 60,
        ]);

        // Create shift policies
        ShiftPolicy::factory()->create(['shift_id' => $this->dayShift->id]);
        ShiftPolicy::factory()->create(['shift_id' => $this->nightShift->id]);

        $this->workZone = WorkZone::factory()->create();
    }

    /** @test */
    public function day_shift_calculates_positive_hours()
    {
        // Assign employee to day shift
        EmployeeShift::create([
            'employee_profile_id' => $this->employeeProfile->id,
            'shift_id' => $this->dayShift->id,
            'work_zone_id' => $this->workZone->id,
            'start_date' => Carbon::today(),
            'end_date' => null,
        ]);

        // Create attendance record
        $attendance = Attendance::create([
            'employee_profile_id' => $this->employeeProfile->id,
            'shift_id' => $this->dayShift->id,
            'check_in_time' => Carbon::today()->setTime(9, 0),
            'check_out_time' => Carbon::today()->setTime(17, 0),
            'status' => 'on_time',
        ]);

        // Test the calculation
        $controller = new \App\Http\Controllers\EmployeeDashboardController();
        $weeklyData = $controller->getWeeklyActivityData($this->employeeProfile, Carbon::today(), Carbon::today());

        $todayName = Carbon::today()->format('D');
        $this->assertGreaterThan(0, $weeklyData[$todayName] ?? 0);
        $this->assertEquals(8, $weeklyData[$todayName]); // 8 hours worked
    }

    /** @test */
    public function night_shift_handles_overnight_correctly()
    {
        // Assign employee to night shift
        EmployeeShift::create([
            'employee_profile_id' => $this->employeeProfile->id,
            'shift_id' => $this->nightShift->id,
            'work_zone_id' => $this->workZone->id,
            'start_date' => Carbon::today(),
            'end_date' => null,
        ]);

        // Create overnight attendance (check-in today 9 PM, check-out tomorrow 5 AM)
        $attendance = Attendance::create([
            'employee_profile_id' => $this->employeeProfile->id,
            'shift_id' => $this->nightShift->id,
            'check_in_time' => Carbon::today()->setTime(21, 0),
            'check_out_time' => Carbon::tomorrow()->setTime(5, 0),
            'status' => 'on_time',
        ]);

        // Test the calculation
        $controller = new \App\Http\Controllers\EmployeeDashboardController();
        $weeklyData = $controller->getWeeklyActivityData($this->employeeProfile, Carbon::today(), Carbon::today());

        $todayName = Carbon::today()->format('D');
        $this->assertGreaterThan(0, $weeklyData[$todayName] ?? 0);
        $this->assertEquals(8, $weeklyData[$todayName]); // 8 hours worked overnight
    }

    /** @test */
    public function incomplete_attendance_shows_reasonable_hours()
    {
        // Assign employee to day shift
        EmployeeShift::create([
            'employee_profile_id' => $this->employeeProfile->id,
            'shift_id' => $this->dayShift->id,
            'work_zone_id' => $this->workZone->id,
            'start_date' => Carbon::today(),
            'end_date' => null,
        ]);

        // Create incomplete attendance (no check-out)
        $attendance = Attendance::create([
            'employee_profile_id' => $this->employeeProfile->id,
            'shift_id' => $this->dayShift->id,
            'check_in_time' => Carbon::today()->setTime(9, 0),
            'check_out_time' => null,
            'status' => 'on_time',
        ]);

        // Test the calculation
        $controller = new \App\Http\Controllers\EmployeeDashboardController();
        $weeklyData = $controller->getWeeklyActivityData($this->employeeProfile, Carbon::today(), Carbon::today());

        $todayName = Carbon::today()->format('D');
        $hours = $weeklyData[$todayName] ?? 0;
        
        $this->assertGreaterThan(0, $hours);
        $this->assertLessThanOrEqual(12, $hours); // Should be capped at 12 hours
    }

    /** @test */
    public function no_negative_hours_in_any_scenario()
    {
        // Test various edge cases that might produce negative hours
        $testCases = [
            // Day shift with reversed times (should not happen but test anyway)
            [
                'shift' => $this->dayShift,
                'check_in' => Carbon::today()->setTime(17, 0),
                'check_out' => Carbon::today()->setTime(9, 0),
            ],
            // Night shift with same day times
            [
                'shift' => $this->nightShift,
                'check_in' => Carbon::today()->setTime(21, 0),
                'check_out' => Carbon::today()->setTime(20, 0),
            ],
        ];

        foreach ($testCases as $case) {
            // Assign employee to shift
            EmployeeShift::create([
                'employee_profile_id' => $this->employeeProfile->id,
                'shift_id' => $case['shift']->id,
                'work_zone_id' => $this->workZone->id,
                'start_date' => Carbon::today(),
                'end_date' => null,
            ]);

            // Create attendance record
            Attendance::create([
                'employee_profile_id' => $this->employeeProfile->id,
                'shift_id' => $case['shift']->id,
                'check_in_time' => $case['check_in'],
                'check_out_time' => $case['check_out'],
                'status' => 'on_time',
            ]);

            // Test the calculation
            $controller = new \App\Http\Controllers\EmployeeDashboardController();
            $weeklyData = $controller->getWeeklyActivityData($this->employeeProfile, Carbon::today(), Carbon::today());

            $todayName = Carbon::today()->format('D');
            $hours = $weeklyData[$todayName] ?? 0;
            
            $this->assertGreaterThanOrEqual(0, $hours, "Hours should never be negative for shift: {$case['shift']->name}");
            
            // Clean up for next test case
            Attendance::where('employee_profile_id', $this->employeeProfile->id)->delete();
            EmployeeShift::where('employee_profile_id', $this->employeeProfile->id)->delete();
        }
    }
}
