#!/bin/bash

# Pre-commit validation script for Attendance Tracker
# Run this before committing changes to catch potential issues

echo "🔍 Running pre-commit validation checks..."
echo "========================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Track if any checks fail
FAILED=0

# Check 1: Run attendance data validation
echo -e "\n${YELLOW}1. Validating attendance data...${NC}"
if php artisan attendance:validate --days=7 > /dev/null 2>&1; then
    echo -e "${GREEN}✅ Attendance data validation passed${NC}"
else
    echo -e "${RED}❌ Attendance data validation failed${NC}"
    echo "Run 'php artisan attendance:validate' for details"
    FAILED=1
fi

# Check 2: Run attendance calculation tests
echo -e "\n${YELLOW}2. Running attendance calculation tests...${NC}"
if php artisan test tests/Feature/AttendanceCalculationTest.php > /dev/null 2>&1; then
    echo -e "${GREEN}✅ Attendance calculation tests passed${NC}"
else
    echo -e "${RED}❌ Attendance calculation tests failed${NC}"
    echo "Run 'php artisan test tests/Feature/AttendanceCalculationTest.php' for details"
    FAILED=1
fi

# Check 3: Verify shift configurations
echo -e "\n${YELLOW}3. Checking shift configurations...${NC}"
SHIFT_CHECK=$(php artisan tinker --execute="
\$issues = [];
\$shifts = App\Models\Shift::all();
foreach (\$shifts as \$shift) {
    \$start = \Carbon\Carbon::parse(\$shift->start_time);
    \$end = \Carbon\Carbon::parse(\$shift->end_time);
    \$isOvernight = \$end->lt(\$start);
    if (\$isOvernight && !\$shift->spans_two_days) {
        \$issues[] = \$shift->name . ' appears overnight but spans_two_days is false';
    }
    if (!\$isOvernight && \$shift->spans_two_days) {
        \$issues[] = \$shift->name . ' marked overnight but times don\'t span midnight';
    }
}
if (empty(\$issues)) {
    echo 'OK';
} else {
    foreach (\$issues as \$issue) {
        echo \$issue . PHP_EOL;
    }
}
" 2>/dev/null)

if [ "$SHIFT_CHECK" = "OK" ]; then
    echo -e "${GREEN}✅ Shift configurations are correct${NC}"
else
    echo -e "${RED}❌ Shift configuration issues found:${NC}"
    echo "$SHIFT_CHECK"
    FAILED=1
fi

# Check 4: Look for potential negative hour calculations in code
echo -e "\n${YELLOW}4. Scanning code for potential negative hour issues...${NC}"
NEGATIVE_PATTERNS=$(grep -r "diffInMinutes\|diffInHours" app/ --include="*.php" | grep -v "max(0" | grep -v "abs(" | wc -l)
if [ "$NEGATIVE_PATTERNS" -gt 0 ]; then
    echo -e "${YELLOW}⚠️  Found $NEGATIVE_PATTERNS time diff calculations that might not handle negatives${NC}"
    echo "Consider adding max(0, ...) or abs() where appropriate"
else
    echo -e "${GREEN}✅ No obvious negative time calculation issues${NC}"
fi

# Check 5: Verify critical files exist
echo -e "\n${YELLOW}5. Checking critical files...${NC}"
CRITICAL_FILES=(
    "CHANGELOG.md"
    "tests/Feature/AttendanceCalculationTest.php"
    "app/Console/Commands/ValidateAttendanceData.php"
)

for file in "${CRITICAL_FILES[@]}"; do
    if [ -f "$file" ]; then
        echo -e "${GREEN}✅ $file exists${NC}"
    else
        echo -e "${RED}❌ $file missing${NC}"
        FAILED=1
    fi
done

# Summary
echo -e "\n========================================"
if [ $FAILED -eq 0 ]; then
    echo -e "${GREEN}🎉 All pre-commit checks passed!${NC}"
    echo "Safe to commit changes."
    exit 0
else
    echo -e "${RED}❌ Some pre-commit checks failed!${NC}"
    echo "Please fix the issues above before committing."
    echo ""
    echo "Quick fixes:"
    echo "- Run: php artisan attendance:validate"
    echo "- Run: php artisan test tests/Feature/AttendanceCalculationTest.php"
    echo "- Check shift configurations in admin panel"
    exit 1
fi
