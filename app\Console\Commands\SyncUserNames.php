<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\User;
use App\Models\EmployeeProfile;

class SyncUserNames extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'users:sync-names {--dry-run : Show what would be changed without making changes}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Synchronize user names with their employee profile names';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $dryRun = $this->option('dry-run');
        
        if ($dryRun) {
            $this->info('Running in dry-run mode. No changes will be made.');
        }

        $profiles = EmployeeProfile::with('user')->get();
        $changesCount = 0;

        $this->info("Found {$profiles->count()} employee profiles to check.");

        foreach ($profiles as $profile) {
            if (!$profile->user) {
                $this->warn("Employee profile {$profile->id} has no associated user.");
                continue;
            }

            $expectedName = $profile->getFullNameAttribute();
            $currentName = $profile->user->name;

            if ($expectedName !== $currentName) {
                $changesCount++;
                
                if ($dryRun) {
                    $this->line("Would change user {$profile->user->id}: '{$currentName}' → '{$expectedName}'");
                } else {
                    $profile->user->update(['name' => $expectedName]);
                    $this->line("Updated user {$profile->user->id}: '{$currentName}' → '{$expectedName}'");
                }
            }
        }

        if ($changesCount === 0) {
            $this->info('All user names are already synchronized with their employee profiles.');
        } else {
            if ($dryRun) {
                $this->info("Found {$changesCount} users that would be updated. Run without --dry-run to apply changes.");
            } else {
                $this->info("Successfully synchronized {$changesCount} user names.");
            }
        }

        return 0;
    }
}
